<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.6.xsd">

    <changeSet id="create-table-subscription-plan" author="ggreco">
        <createTable tableName="subscription_plan">
            <column name="code" type="varchar">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="price_monthly" type="decimal(10,2)">
                <constraints nullable="false"/>
            </column>
            <column name="limits" type="jsonb">
                <constraints nullable="true"/>
            </column>

            <column name="create_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="update_date" type="timestamp with timezone">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <rollback>
            <dropTable tableName="subscription_plan"/>
        </rollback>
    </changeSet>

    <changeSet id="insert-subscription-plans" author="ggreco">
        <insert tableName="subscription_plan">
            <column name="code" value="STARTER"/>
            <column name="price_monthly" value="40000"/>
            <column name="limits" value='{"maxMonthlyTransactions": "500", "maxBranches": "1"}'/>
            <column name="create_date" value="now()"/>
            <column name="update_date" value="now()"/>
            <column name="version" value="1"/>
        </insert>
        <insert tableName="subscription_plan">
            <column name="code" value="STANDARD"/>
            <column name="price_monthly" value="60000"/>
            <column name="limits" value='{"maxMonthlyTransactions": "1000", "maxBranches": "2"}'/>
            <column name="create_date" value="now()"/>
            <column name="update_date" value="now()"/>
            <column name="version" value="1"/>
        </insert>
        <insert tableName="subscription_plan">
            <column name="code" value="CORPORATE"/>
            <column name="price_monthly" value="150000"/>
            <column name="limits" value='{"maxMonthlyTransactions": "5000"}'/>
            <column name="create_date" value="now()"/>
            <column name="update_date" value="now()"/>
            <column name="version" value="1"/>
        </insert>
    </changeSet>

</databaseChangeLog>
