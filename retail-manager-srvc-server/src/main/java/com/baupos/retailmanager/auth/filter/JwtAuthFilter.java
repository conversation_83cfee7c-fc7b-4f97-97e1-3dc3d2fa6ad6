package com.baupos.retailmanager.auth.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.baupos.retailmanager.auth.service.AccessTokenAuthentication;
import com.baupos.retailmanager.auth.service.AuthUserDetailsService;
import com.baupos.retailmanager.auth.service.AuthenticationCookieManager;
import com.baupos.retailmanager.auth.service.JwtUtil;
import com.baupos.retailmanager.auth.service.sto.AuthUserDetailsSto;
import com.baupos.retailmanager.common.exception.RestExceptionHandler;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * Intercepts requests and looks for and Authorization header, if present,
 * validates the token and loads the user details from the DB into the authentication context.
 * Requests without a valid Authorization header continue through the filter chain,
 * while authenticated users gain access to protected resources.
 */
@RequiredArgsConstructor
@Slf4j
@Component
public class JwtAuthFilter extends OncePerRequestFilter {

    private final RestExceptionHandler restExceptionHandler;
    private final ObjectMapper objectMapper;
    private final AuthUserDetailsService authUserDetailsService;
    private final AuthenticationCookieManager authenticationCookieManager;
    private final JwtUtil jwtUtil;

    /**
     * Allows skipping initialization of security context when not needed,
     * Expired tokens cause an exception, we need to prevent that otherwise users would
     * not be able to log in again.
     * TODO: Investigate if not failing with an exception would be better, leaving the
     * security context empty instead, and failing downstream on auth checks.
     * Expired token error should be special, in that case, should we fail here or on auth checks?
     */
    private final List<RequestMatcher> publicRoutesRequestMatchers = List.of(
            new AntPathRequestMatcher("/v1/auth/login"),
            new AntPathRequestMatcher("/v1/auth/logout"),
            new AntPathRequestMatcher("/v1/auth/password-reset-token"),
            new AntPathRequestMatcher("/v1/auth/reset-password"),
            new AntPathRequestMatcher("/v1/onboarding/subscription"),
            new AntPathRequestMatcher("/v1/subscription-plans"),
            new AntPathRequestMatcher("/api-docs/*"),
            new AntPathRequestMatcher("/swagger-ui/*")
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            if (this.publicRoutesRequestMatchers.stream().noneMatch(requestMatcher -> requestMatcher.matches(request))) {
                initializeSecurityContextFromAccessToken(request, response, filterChain);
            }
            filterChain.doFilter(request, response);
        } catch (JwtException e) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            objectMapper.writeValue(response.getWriter(), restExceptionHandler.jwtException(e));
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            objectMapper.writeValue(response.getWriter(), restExceptionHandler.defaultHandler(e));
        }
    }

    private void initializeSecurityContextFromAccessToken(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) {
        String authHeader = request.getHeader("Authorization");
        String token = null;
        String username = null;
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
            username = jwtUtil.extractUsername(token);
        }

        // If cookie is set, use that instead
        Optional<String> accessTokenCookie = authenticationCookieManager.getAccessTokenCookie(request);
        if (accessTokenCookie.isPresent()) {
            token = accessTokenCookie.get();
            username = jwtUtil.extractUsername(accessTokenCookie.get());
        }

        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            // Load user details from the DB
            // Alternatively we could parse the user details from the claims on JWT token,
            // but since this resource server is also the auth server, we use the DB to avoid
            // having to expire tokens when privileges change.
            AuthUserDetailsSto userDetails = authUserDetailsService.loadUserByUsername(username);
            if (jwtUtil.validateToken(token, userDetails)) {
                AccessTokenAuthentication authenticationToken = new AccessTokenAuthentication(userDetails, null, userDetails.getAuthorities(), token);
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            }
        }
    }

}
