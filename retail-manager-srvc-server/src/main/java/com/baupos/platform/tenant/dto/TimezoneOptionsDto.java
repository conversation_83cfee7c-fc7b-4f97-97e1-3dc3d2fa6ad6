package com.baupos.platform.tenant.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;

@Builder
@Getter
@RequiredArgsConstructor
public class TimezoneOptionsDto {
    private final Map<String, String> timezones;

    @JsonCreator
    public TimezoneOptionsDto(@JsonProperty("timezones") Map<String, String> timezones) {
        this.timezones = timezones;
    }
}
