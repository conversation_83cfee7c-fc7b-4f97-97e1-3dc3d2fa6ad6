package com.baupos.platform.tenant;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import com.baupos.platform.tenant.dto.TenantSettingsDto;
import com.baupos.platform.tenant.dto.TimezoneOptionsDto;
import com.baupos.platform.tenant.dto.UpdateTenantSettingsRequestDto;
import com.baupos.platform.tenant.service.TenantSettingsMapper;
import com.baupos.platform.tenant.service.TenantSettingsService;
import com.baupos.platform.tenant.service.sto.TenantSettingsSto;
import com.baupos.platform.tenant.service.sto.UpdateTenantSettingsRequestSto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Slf4j
@RestController
@Validated
@RequestMapping("/v1/tenant/settings")
@RequiredArgsConstructor
public class TenantSettingsController {

    private final TenantSettingsService tenantSettingsService;
    private final TenantSettingsMapper tenantSettingsMapper;
    private final AuthenticationFacade authenticationFacade;

    @GetMapping("/")
    @PreAuthorize("@authorizationChecks.canReadTenantSettings()")
    public ResponseEntity<TenantSettingsDto> getTenantSettings() {
        UUID tenantId = authenticationFacade.getAuthenticatedTenantId().get();
        
        return tenantSettingsService.findByTenantId(tenantId)
                .map(tenantSettingsMapper::mapToDto)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.ok(TenantSettingsDto.builder().build()));
    }

    @PutMapping("/")
    @PreAuthorize("@authorizationChecks.canUpdateTenantSettings()")
    public TenantSettingsDto updateTenantSettings(@RequestBody @Valid UpdateTenantSettingsRequestDto updateRequest) {
        UUID tenantId = authenticationFacade.getAuthenticatedTenantId().get();
        
        TenantSettingsSto updatedSettings = tenantSettingsService.updateTenantSettings(
                UpdateTenantSettingsRequestSto.builder()
                        .tenantId(tenantId)
                        .companyName(updateRequest.getCompanyName())
                        .locale(updateRequest.getLocale())
                        .timezone(updateRequest.getTimezone())
                        .emailAddress(updateRequest.getEmailAddress())
                        .phoneNumber(updateRequest.getPhoneNumber())
                        .country(updateRequest.getCountry())
                        .city(updateRequest.getCity())
                        .address(updateRequest.getAddress())
                        .build());
        
        return tenantSettingsMapper.mapToDto(updatedSettings);
    }

    @GetMapping("/timezone-options")
    @PreAuthorize("@authorizationChecks.canReadTenantSettings()")
    public TimezoneOptionsDto getTimezoneOptions() {
        UUID tenantId = authenticationFacade.getAuthenticatedTenantId().get();

        return TimezoneOptionsDto.builder()
                .timezones(tenantSettingsService.getTimezoneOptions(tenantId))
                .build();
    }
}
