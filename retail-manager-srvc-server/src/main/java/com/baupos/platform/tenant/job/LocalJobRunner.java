package com.baupos.platform.tenant.job;

import com.baupos.platform.billing.job.TenantInvoiceGenerationJob;
import com.baupos.platform.subscription.job.TenantOverdueSubscriptionSuspensionJob;
import com.baupos.platform.subscription.job.TenantTrialSubscriptionTerminationJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LocalJobRunner {

    private final TenantInvoiceGenerationJob tenantInvoiceGenerationJob;
    private final TenantOverdueSubscriptionSuspensionJob tenantOverdueSubscriptionSuspensionJob;
    private final TenantTrialSubscriptionTerminationJob tenantTrialSubscriptionTerminationJob;

    //@EventListener(ApplicationReadyEvent.class)
    public void doSomethingAfterStartup() {
        // just for testing
        tenantTrialSubscriptionTerminationJob.terminateExpiredTrialSubscriptions();
        tenantInvoiceGenerationJob.generateMonthlyInvoicesForActiveSubscriptions();

    }
}
