package com.baupos.platform.tenant.service;

import com.baupos.platform.tenant.domain.TenantSettings;
import com.baupos.platform.tenant.dto.TenantSettingsDto;
import com.baupos.platform.tenant.service.sto.TenantSettingsSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class TenantSettingsMapper {

    @Transactional(propagation = Propagation.MANDATORY)
    public TenantSettingsSto map(TenantSettings tenantSettings) {
        return TenantSettingsSto.builder()
                .tenantId(tenantSettings.getTenantId())
                .companyName(tenantSettings.getCompanyName())
                .locale(Optional.ofNullable(tenantSettings.getLocale()).orElse("es_AR"))
                .timezone(Optional.ofNullable(tenantSettings.getTimezone()).orElse("America/Argentina/Buenos_Aires"))
                .emailAddress(tenantSettings.getEmailAddress())
                .phoneNumber(tenantSettings.getPhoneNumber())
                .country(Optional.ofNullable(tenantSettings.getCountry()).orElse("Argentina"))
                .city(tenantSettings.getCity())
                .address(tenantSettings.getAddress())
                .build();
    }

    public TenantSettingsDto mapToDto(TenantSettingsSto tenantSettingsSto) {
        return TenantSettingsDto.builder()
                .companyName(tenantSettingsSto.getCompanyName())
                .locale(tenantSettingsSto.getLocale())
                .timezone(tenantSettingsSto.getTimezone())
                .emailAddress(tenantSettingsSto.getEmailAddress())
                .phoneNumber(tenantSettingsSto.getPhoneNumber())
                .country(tenantSettingsSto.getCountry())
                .city(tenantSettingsSto.getCity())
                .address(tenantSettingsSto.getAddress())
                .build();
    }
}
