package com.baupos.platform.tenant.service;

import com.baupos.platform.plan.domain.SubscriptionPlan;
import com.baupos.platform.plan.domain.SubscriptionPlanCode;
import com.baupos.platform.subscription.domain.TenantSubscription;
import com.baupos.platform.subscription.domain.TenantSubscriptionRepository;
import com.baupos.platform.subscription.domain.TenantSubscriptionStatus;
import com.baupos.platform.tenant.domain.Tenant;
import com.baupos.platform.tenant.domain.TenantRepository;
import com.baupos.platform.tenant.service.sto.TenantSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class TenantService {

    private final TenantRepository tenantRepository;
    private final TenantSubscriptionRepository tenantSubscriptionRepository;
    private final TenantMapper tenantMapper;

    @Transactional
    public TenantSto createTenantAndTrialSubscription() {
        Tenant tenant = tenantRepository.save(Tenant.builder()
                .build());

        tenantSubscriptionRepository.save(TenantSubscription.builder()
                .tenantId(tenant.getId())
                .plan(SubscriptionPlan.builder().code(SubscriptionPlanCode.STARTER).build())
                .status(TenantSubscriptionStatus.TRIAL)
                .build());

        return tenantMapper.map(tenant);
    }

}
