package com.baupos.platform.plan.domain;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SubscriptionPlanRepository extends JpaRepository<SubscriptionPlan, SubscriptionPlanCode> {

    List<SubscriptionPlan> findAllByOrderByCreateDateAsc();

    Optional<SubscriptionPlan> findByCode(SubscriptionPlanCode code);

}
