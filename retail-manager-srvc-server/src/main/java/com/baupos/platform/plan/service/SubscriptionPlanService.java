package com.baupos.platform.plan.service;

import com.baupos.platform.plan.domain.SubscriptionPlanCode;
import com.baupos.platform.plan.domain.SubscriptionPlanRepository;
import com.baupos.platform.plan.service.sto.SubscriptionPlanSto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionPlanService {

    private final SubscriptionPlanRepository subscriptionPlanRepository;
    private final SubscriptionPlanMapper subscriptionPlanMapper;

    @Transactional(readOnly = true)
    public List<SubscriptionPlanSto> findAll() {
        return subscriptionPlanRepository.findAllByOrderByCreateDateAsc()
                .stream()
                .map(subscriptionPlanMapper::mapToSto)
                .toList();
    }

    @Transactional(readOnly = true)
    public SubscriptionPlanSto getByCode(SubscriptionPlanCode code) {
        return subscriptionPlanRepository.findById(code).map(subscriptionPlanMapper::mapToSto).orElseThrow();
    }

}
