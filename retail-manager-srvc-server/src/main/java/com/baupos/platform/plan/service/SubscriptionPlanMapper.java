package com.baupos.platform.plan.service;

import com.baupos.platform.plan.domain.SubscriptionPlan;
import com.baupos.platform.plan.dto.SubscriptionPlanDto;
import com.baupos.platform.plan.service.sto.SubscriptionPlanSto;
import org.springframework.stereotype.Component;

@Component
public class SubscriptionPlanMapper {

    public SubscriptionPlanDto mapToDto(SubscriptionPlanSto subscriptionPlan) {
        return SubscriptionPlanDto.builder()
                .code(subscriptionPlan.getCode())
                .priceMonthly(subscriptionPlan.getPriceMonthly())
                .limits(subscriptionPlan.getLimits())
                .build();
    }

    public SubscriptionPlanSto mapToSto(SubscriptionPlan subscriptionPlan) {
        return SubscriptionPlanSto.builder()
                .code(subscriptionPlan.getCode())
                .priceMonthly(subscriptionPlan.getPriceMonthly())
                .limits(subscriptionPlan.getLimits())
                .build();
    }

}
