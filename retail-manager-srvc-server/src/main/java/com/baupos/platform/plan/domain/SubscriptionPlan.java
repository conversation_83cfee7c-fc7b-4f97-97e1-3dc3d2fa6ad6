package com.baupos.platform.plan.domain;

import com.baupos.retailmanager.common.domain.DatedEntity;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;

@Entity
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class SubscriptionPlan extends DatedEntity {

    @Id
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private SubscriptionPlanCode code;

    @Column(nullable = false)
    private BigDecimal priceMonthly;

    @Column(columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private SubscriptionPlanLimitsSto limits;

}
