package com.baupos.platform.plan.service.sto;

import com.baupos.platform.plan.domain.SubscriptionPlanCode;
import com.baupos.platform.plan.domain.SubscriptionPlanLimitsSto;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;

@Builder
@Getter
@RequiredArgsConstructor
public class SubscriptionPlanSto {
    private final SubscriptionPlanCode code;
    private final BigDecimal priceMonthly;
    private final SubscriptionPlanLimitsSto limits;
}
