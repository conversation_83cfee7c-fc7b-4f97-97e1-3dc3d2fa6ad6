package com.baupos.platform.subscription.dto;

import com.baupos.platform.billing.dto.TenantInvoiceDto;
import com.baupos.platform.plan.dto.SubscriptionPlanDto;
import com.baupos.platform.subscription.domain.TenantSubscriptionStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
public class TenantSubscriptionDto {
    private final UUID id;
    private final SubscriptionPlanDto plan;
    private final TenantSubscriptionStatus status;
    private final OffsetDateTime createDate;
    private final List<TenantInvoiceDto> invoices;

    @JsonProperty
    public OffsetDateTime getNextDueDate() {
        return invoices.stream()
                .map(TenantInvoiceDto::getDueDate)
                .max(OffsetDateTime::compareTo)
                .orElse(null);
    }

    @JsonProperty
    public OffsetDateTime getTrialExpirationDate() {
        // TODO: find out best way to use ${subscription.trialDuration}
        return createDate.plusDays(15);
    }
}
