package com.baupos.platform.subscription;

import com.baupos.retailmanager.auth.service.AuthenticationFacade;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmNotFoundException;
import com.baupos.platform.billing.dto.CreateRecurringPaymentRequestDto;
import com.baupos.platform.subscription.dto.TenantSubscriptionDto;
import com.baupos.platform.subscription.service.TenantSubscriptionMapper;
import com.baupos.platform.subscription.service.TenantSubscriptionService;
import com.baupos.platform.subscription.service.sto.TenantSubscriptionSto;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/v1/subscriptions")
public class SubscriptionController {

    private final AuthenticationFacade authenticationFacade;
    private final TenantSubscriptionService tenantSubscriptionService;
    private final TenantSubscriptionMapper tenantSubscriptionMapper;

    @GetMapping("/primary")
    @PreAuthorize("@authorizationChecks.isLoggedIn()")
    public ResponseEntity<TenantSubscriptionDto> getPrimarySubscription(HttpServletRequest httpRequest,
                                                                        HttpServletResponse httpResponse) {
        UUID authenticatedUserTenantId = authenticationFacade.getAuthenticatedTenantId().get();
        TenantSubscriptionSto subscriptionSto = tenantSubscriptionService
                .findPrimarySubscriptionByTenantId(authenticatedUserTenantId)
                .orElseThrow(() -> new CmNotFoundException(CmErrorCode.SUBSCRIPTION_NOT_FOUND
                        .details(authenticatedUserTenantId)));

        return ResponseEntity.ok(tenantSubscriptionMapper.mapToDto(subscriptionSto));
    }

    /**
     * Activates a subscription.
     * TODO: receive a request body, containing:
     * - the plan code to activate
     * - the card token
     * - the payer email
     * - the authorized amount -> validate it matches stored plan price
     * @param subscriptionId
     * @return
     */
    @PostMapping("{subscriptionId}/activate")
    public ResponseEntity<TenantSubscriptionDto> activateSubscription(@PathVariable("subscriptionId") UUID subscriptionId) {
        TenantSubscriptionSto subscription = tenantSubscriptionService.activateSubscription(subscriptionId);
        return ResponseEntity.ok(tenantSubscriptionMapper.mapToDto(subscription));
    }

    /**
     * Sets up a recurring payment for the provided subscriptionId and amount using an external payment gateway.
     *
     * @param httpRequest
     * @param httpResponse
     * @param request
     * @return
     */
    @PostMapping("/recurring-payment")
    public ResponseEntity<Void> setupRecurringPayment(HttpServletRequest httpRequest,
                                                       HttpServletResponse httpResponse,
                                                       @Valid @RequestBody CreateRecurringPaymentRequestDto request) {
        tenantSubscriptionService.setUpRecurringPayment(request);
        return ResponseEntity.status(HttpStatus.OK).build();
    }


}
