package com.baupos.platform.subscription.service;

import com.baupos.platform.billing.service.TenantInvoiceMapper;
import com.baupos.platform.plan.service.SubscriptionPlanMapper;
import com.baupos.platform.subscription.domain.TenantSubscription;
import com.baupos.platform.subscription.dto.TenantSubscriptionDto;
import com.baupos.platform.subscription.service.sto.TenantSubscriptionSto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
public class TenantSubscriptionMapper {

    private final SubscriptionPlanMapper subscriptionPlanMapper;
    private final TenantInvoiceMapper tenantInvoiceMapper;

    @Transactional(propagation = Propagation.REQUIRED)
    public TenantSubscriptionSto mapToSto(TenantSubscription tenantSubscription) {
        return TenantSubscriptionSto.builder()
                .id(tenantSubscription.getId())
                .tenantId(tenantSubscription.getTenantId())
                .plan(subscriptionPlanMapper.mapToSto(tenantSubscription.getPlan()))
                .status(tenantSubscription.getStatus())
                .activationDate(tenantSubscription.getActivationDate())
                .createDate(tenantSubscription.getCreateDate())
                .invoices(tenantSubscription.getInvoices().stream().map(tenantInvoiceMapper::mapToSto).toList())
                .build();
    }

    public TenantSubscriptionDto mapToDto(TenantSubscriptionSto tenantSubscription) {
        return TenantSubscriptionDto.builder()
                .id(tenantSubscription.getId())
                .plan(subscriptionPlanMapper.mapToDto(tenantSubscription.getPlan()))
                .status(tenantSubscription.getStatus())
                .createDate(tenantSubscription.getCreateDate())
                .invoices(tenantSubscription.getInvoices().stream().map(tenantInvoiceMapper::mapToDto).toList())
                .build();
    }
}
