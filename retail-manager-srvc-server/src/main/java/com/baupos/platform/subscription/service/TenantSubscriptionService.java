package com.baupos.platform.subscription.service;

import com.baupos.platform.billing.service.TenantInvoiceService;
import com.mercadopago.resources.preapproval.Preapproval;
import com.baupos.retailmanager.common.error.CmErrorCode;
import com.baupos.retailmanager.common.exception.CmBadRequestException;
import com.baupos.retailmanager.common.utils.DateUtils;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.platform.subscription.domain.TenantSubscription;
import com.baupos.platform.billing.domain.TenantSubscriptionRecurringPayment;
import com.baupos.platform.billing.domain.TenantSubscriptionRecurringPaymentRepository;
import com.baupos.platform.subscription.domain.TenantSubscriptionRepository;
import com.baupos.platform.billing.dto.CreateRecurringPaymentRequestDto;
import com.baupos.platform.subscription.event.SubscriptionSuspendedEvent;
import com.baupos.platform.subscription.event.TrialSubscriptionEndedEvent;
import com.baupos.platform.billing.external.service.PaymentGatewayService;
import com.baupos.platform.subscription.service.sto.TenantSubscriptionSto;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Clock;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.UUID;

import static com.baupos.platform.subscription.domain.TenantSubscriptionStatus.*;

@Service
@RequiredArgsConstructor
public class TenantSubscriptionService {
    private static final int SUBSCRIPTION_PROCESSING_BATCH_SIZE = 20;

    @Value("${subscription.trialDuration}")
    private Duration trialDuration;
    private final TenantInvoiceService tenantInvoiceService;
    private final PaymentGatewayService paymentGatewayService;
    private final TenantSubscriptionRepository tenantSubscriptionRepository;
    private final TenantSubscriptionRecurringPaymentRepository tenantSubscriptionRecurringPaymentRepository;
    private final TenantSubscriptionMapper tenantSubscriptionMapper;
    private final TransactionTaskRunner transactionTaskRunner;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final DateUtils dateUtils;
    private final Clock clock;

    /**
     * Finds the subscription for a given tenant.
     * Even though the data model allows for multiple subscriptions on a single tenant,
     * we currently use a single subscription so this finder returns the first match.
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public Optional<TenantSubscriptionSto> findPrimarySubscriptionByTenantId(UUID tenantId) {
        return tenantSubscriptionRepository.findByTenantId(tenantId).stream().findFirst()
                .map(tenantSubscriptionMapper::mapToSto);
    }

    @Transactional(readOnly = true)
    public Slice<TenantSubscriptionSto> findExpiredTrialSubscriptions() {
        return tenantSubscriptionRepository
                .findByStatusAndCreateDateBefore(TRIAL, OffsetDateTime.now(clock).minus(trialDuration), PageRequest.of(0, SUBSCRIPTION_PROCESSING_BATCH_SIZE))
                .map(tenantSubscriptionMapper::mapToSto);
    }

    @Transactional(propagation = Propagation.NEVER)
    public void terminateTrialSubscription(TenantSubscriptionSto tenantSubscriptionSto) {
        transactionTaskRunner.readWrite(() -> tenantSubscriptionRepository
                .updateSubscriptionStatus(tenantSubscriptionSto.getId(), TRIAL_ENDED));

        applicationEventPublisher.publishEvent(TrialSubscriptionEndedEvent.builder()
                .tenantId(tenantSubscriptionSto.getTenantId())
                .build());
    }

    @Transactional(propagation = Propagation.NEVER)
    public void suspendSubscription(TenantSubscriptionSto tenantSubscriptionSto) {
        transactionTaskRunner.readWrite(() -> tenantSubscriptionRepository
                .updateSubscriptionStatus(tenantSubscriptionSto.getId(), SUSPENDED));

        applicationEventPublisher.publishEvent(SubscriptionSuspendedEvent.builder()
                .tenantId(tenantSubscriptionSto.getTenantId())
                .build());
    }

    @Transactional(readOnly = true)
    public Slice<TenantSubscriptionSto> findSubscriptionsPendingMonthlyInvoiceGeneration() {
        return tenantSubscriptionRepository
                .findActiveSubscriptionsWithNoInvoiceAfterDate(dateUtils.getStartOfMonth(), PageRequest.of(0, SUBSCRIPTION_PROCESSING_BATCH_SIZE))
                .map(tenantSubscriptionMapper::mapToSto);
    }

    @Transactional(readOnly = true)
    public Slice<TenantSubscriptionSto> findActiveSubscriptionsWithOverDueInvoice() {
        return tenantSubscriptionRepository
                .findActiveSubscriptionsWithOverDueInvoice(PageRequest.of(0, SUBSCRIPTION_PROCESSING_BATCH_SIZE))
                .map(tenantSubscriptionMapper::mapToSto);
    }

    @Transactional
    public TenantSubscriptionSto activateSubscription(UUID subscriptionId) {
        TenantSubscription subscription = tenantSubscriptionRepository.getReferenceById(subscriptionId);
        subscription.setStatus(ACTIVE);
        subscription.setActivationDate(OffsetDateTime.now(clock));
        subscription = tenantSubscriptionRepository.save(subscription);
        tenantInvoiceService.generateMonthlyInvoiceForSubscription(tenantSubscriptionMapper.mapToSto(subscription));

        return tenantSubscriptionMapper.mapToSto(tenantSubscriptionRepository.getReferenceById(subscriptionId));
    }

    @Transactional(propagation = Propagation.NEVER)
    public void setUpRecurringPayment(CreateRecurringPaymentRequestDto request) {
        TenantSubscriptionSto tenantSubscription = getById(request.getSubscriptionId());

        if (tenantSubscription.getStatus() != ACTIVE) {
            throw new CmBadRequestException(CmErrorCode.SUBSCRIPTION_NOT_ACTIVE.details(tenantSubscription));
        }

        transactionTaskRunner.readOnly(() -> tenantSubscriptionRecurringPaymentRepository
                .findBySubscriptionIdAndAmount(tenantSubscription.getId(), request.getAmount())
                .stream()
                .findFirst()
                .ifPresent(rp -> {
                    throw new CmBadRequestException(CmErrorCode.RECURRING_PAYMENT_ALREADY_EXISTS.details(rp));
                }));

        Preapproval subscriptionPreapproval = paymentGatewayService.createMonthlyRecurringPayment(request.getAmount(),
                request.getCardToken(), request.getPayerEmail(), request.getSubscriptionId(),
                buildRecurringPaymentDesc(tenantSubscription));

        transactionTaskRunner.readWrite(() -> tenantSubscriptionRecurringPaymentRepository.save(
                TenantSubscriptionRecurringPayment.builder()
                        .tenantId(tenantSubscription.getTenantId())
                        .subscription(tenantSubscriptionRepository.getReferenceById(tenantSubscription.getId()))
                        .amount(subscriptionPreapproval.getAutoRecurring().getTransactionAmount())
                        .nextPaymentDate(subscriptionPreapproval.getNextPaymentDate())
                        .reason(subscriptionPreapproval.getReason())
                        .externalStatus(subscriptionPreapproval.getStatus())
                        .externalId(subscriptionPreapproval.getId())
                        .externalPayerId(String.valueOf(subscriptionPreapproval.getPayerId()))
                        .externalPayerEmail(subscriptionPreapproval.getPayerEmail())
                        .build()));

    }

    public TenantSubscriptionSto getById(String subscriptionId) {
        return transactionTaskRunner.readOnlyAndGet(() -> tenantSubscriptionRepository
                .findById(UUID.fromString(subscriptionId))
                .map(tenantSubscriptionMapper::mapToSto)
                .orElseThrow(() -> new CmBadRequestException(CmErrorCode.SUBSCRIPTION_NOT_FOUND.details(subscriptionId)))
        );
    }

    @Transactional(readOnly = true)
    public Optional<TenantSubscriptionSto> findByRecurringPaymentExternalId(String recurringPaymentExternalId) {
        return tenantSubscriptionRecurringPaymentRepository.findByExternalId(recurringPaymentExternalId)
                .map(TenantSubscriptionRecurringPayment::getSubscription)
                .map(tenantSubscriptionMapper::mapToSto);
    }

    private String buildRecurringPaymentDesc(TenantSubscriptionSto tenantSubscription) {
        return String.format("Suscripción mensual a plan %s", tenantSubscription.getPlan().getCode().name());
    }

}
