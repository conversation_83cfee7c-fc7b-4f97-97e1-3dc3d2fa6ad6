package com.baupos.platform.subscription.service.sto;

import com.baupos.platform.billing.service.sto.TenantInvoiceSto;
import com.baupos.platform.plan.service.sto.SubscriptionPlanSto;
import com.baupos.platform.subscription.domain.TenantSubscriptionStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@Builder
@RequiredArgsConstructor
public class TenantSubscriptionSto {
    private final UUID id;
    private final UUID tenantId;
    private final SubscriptionPlanSto plan;
    private final TenantSubscriptionStatus status;
    private final OffsetDateTime activationDate;
    private final OffsetDateTime createDate;
    private final List<TenantInvoiceSto> invoices;
}
