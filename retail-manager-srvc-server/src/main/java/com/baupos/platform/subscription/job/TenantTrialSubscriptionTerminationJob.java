package com.baupos.platform.subscription.job;

import com.baupos.platform.subscription.service.TenantSubscriptionService;
import com.baupos.platform.subscription.service.sto.TenantSubscriptionSto;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Slice;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TenantTrialSubscriptionTerminationJob {

    private final TenantSubscriptionService tenantSubscriptionService;

    @Scheduled(cron = "0 0 0 1 1/1 *")
    public void terminateExpiredTrialSubscriptions() {
        Slice<TenantSubscriptionSto> expiredSubscriptions;
        do {
            expiredSubscriptions = tenantSubscriptionService.findExpiredTrialSubscriptions();
            expiredSubscriptions.forEach(tenantSubscriptionService::terminateTrialSubscription);
        } while (expiredSubscriptions.hasNext());
    }
}