package com.baupos.platform.billing.service;

import com.baupos.platform.billing.domain.*;
import com.baupos.platform.billing.event.TenantInvoiceCreatedEvent;
import com.baupos.platform.billing.external.eto.MpAuthorizedPaymentEto;
import com.baupos.platform.billing.service.sto.TenantInvoiceSto;
import com.baupos.platform.subscription.domain.TenantSubscription;
import com.baupos.platform.subscription.service.sto.TenantSubscriptionSto;
import com.baupos.retailmanager.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class TenantInvoiceService {

    @Value("${invoice.dueDate}")
    private Duration invoiceDueDate;

    private final TenantInvoiceRepository tenantInvoiceRepository;
    private final TenantInvoicePaymentRepository tenantInvoicePaymentRepository;
    private final TenantSubscriptionRecurringPaymentRepository tenantSubscriptionRecurringPaymentRepository;
    private final TenantInvoiceMapper tenantInvoiceMapper;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final DateUtils dateUtils;
    private final Clock clock;

    @Transactional
    public void generateMonthlyInvoiceForSubscription(TenantSubscriptionSto subscription) {
        TenantInvoice invoice = TenantInvoice.builder()
                .tenantId(subscription.getTenantId())
                .status(TenantInvoiceStatus.PAYMENT_PENDING)
                .issueDate(OffsetDateTime.now(clock))
                .dueDate(OffsetDateTime.now(clock).plus(invoiceDueDate))
                .subscription(TenantSubscription.builder().id(subscription.getId()).build())
                .build();

        invoice.addItem(TenantInvoiceItem.builder()
                .from(dateUtils.getStartOfMonth())
                .to(dateUtils.getStartOfNextMonth())
                .price(getSubscriptionPrice(subscription))
                .quantity(BigDecimal.ONE)
                .itemName(getSubscriptionItemName(subscription))
                .build());

        TenantInvoice newInvoice = tenantInvoiceRepository.save(invoice);

        applicationEventPublisher.publishEvent(TenantInvoiceCreatedEvent.builder()
                .invoiceId(newInvoice.getId())
                .tenantId(invoice.getTenantId())
                .invoiceTotal(invoice.getInvoiceTotal())
                .dueDate(invoice.getDueDate())
                .build()
        );
    }

    @Transactional(readOnly = true)
    public Optional<TenantInvoiceSto> findPendingBySubscriptionIdAndIssueDateAfterAndTotal(UUID subscriptionId,
                                                                                           OffsetDateTime issueDateAfter,
                                                                                           BigDecimal amount) {
        return tenantInvoiceRepository.findBySubscriptionIdAndStatusAndIssueDateAfterAndInvoiceTotal(subscriptionId,
                        TenantInvoiceStatus.PAYMENT_PENDING, issueDateAfter, amount)
                .map(tenantInvoiceMapper::mapToSto);
    }

    private BigDecimal getSubscriptionPrice(TenantSubscriptionSto subscription) {
        return subscription.getPlan().getPriceMonthly();
    }

    private String getSubscriptionItemName(TenantSubscriptionSto subscription) {
        return String.format("SUBSCRIPTION_%s", subscription.getPlan().getCode().name());
    }

    @Transactional
    public void updateStatus(UUID id, TenantInvoiceStatus newStatus) {
        TenantInvoice invoice = tenantInvoiceRepository.getReferenceById(id);
        invoice.setStatus(newStatus);
        tenantInvoiceRepository.save(invoice);
    }

    @Transactional
    public void recordInvoicePayment(UUID invoiceId, MpAuthorizedPaymentEto paymentEto) {
        TenantInvoice invoice = tenantInvoiceRepository.getReferenceById(invoiceId);
        TenantInvoicePayment newPayment = tenantInvoicePaymentRepository.save(TenantInvoicePayment.builder()
                .invoice(invoice)
                .recurringPayment(tenantSubscriptionRecurringPaymentRepository.findByExternalId(paymentEto.getPreapprovalId()).get())
                .tenantId(invoice.getTenantId())
                .amount(paymentEto.getTransactionAmount())
                .externalStatus(String.format("%s - %s", paymentEto.getPayment().getStatus(), paymentEto.getPayment().getStatusDetail()))
                .externalReason(paymentEto.getReason())
                .externalId(String.valueOf(paymentEto.getId()))
                .build());

        invoice.setStatus(TenantInvoiceStatus.PAYMENT_COMPLETE);
        tenantInvoiceRepository.save(invoice);
    }

}
