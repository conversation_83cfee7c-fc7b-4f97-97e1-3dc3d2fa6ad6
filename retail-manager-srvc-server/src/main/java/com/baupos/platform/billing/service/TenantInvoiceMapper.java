package com.baupos.platform.billing.service;

import com.baupos.platform.billing.domain.TenantInvoice;
import com.baupos.platform.billing.domain.TenantInvoiceItem;
import com.baupos.platform.billing.dto.TenantInvoiceDto;
import com.baupos.platform.billing.dto.TenantInvoiceItemDto;
import com.baupos.platform.billing.service.sto.TenantInvoiceItemSto;
import com.baupos.platform.billing.service.sto.TenantInvoiceSto;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
public class TenantInvoiceMapper {

    public TenantInvoiceSto mapToSto(TenantInvoice tenantInvoice) {
        return TenantInvoiceSto.builder()
                .id(tenantInvoice.getId())
                .tenantId(tenantInvoice.getTenantId())
                .status(tenantInvoice.getStatus())
                .issueDate(tenantInvoice.getIssueDate())
                .dueDate(tenantInvoice.getDueDate())
                .items(tenantInvoice.getItems()
                        .stream()
                        .map(item -> mapToItemSto(item))
                        .collect(Collectors.toList()))
                .build();
    }

    public TenantInvoiceDto mapToDto(TenantInvoiceSto tenantInvoiceSto) {
        return TenantInvoiceDto.builder()
                .id(tenantInvoiceSto.getId())
                .tenantId(tenantInvoiceSto.getTenantId())
                .status(tenantInvoiceSto.getStatus())
                .issueDate(tenantInvoiceSto.getIssueDate())
                .dueDate(tenantInvoiceSto.getDueDate())
                .items(tenantInvoiceSto.getItems()
                        .stream()
                        .map(item -> mapToItemDto(item))
                        .collect(Collectors.toList()))
                .build();
    }

    private TenantInvoiceItemSto mapToItemSto(TenantInvoiceItem invoiceItem) {
        return TenantInvoiceItemSto.builder()
                .itemName(invoiceItem.getItemName())
                .from(invoiceItem.getFrom())
                .to(invoiceItem.getTo())
                .price(invoiceItem.getPrice())
                .quantity(invoiceItem.getQuantity())
                .build();
    }

    private TenantInvoiceItemDto mapToItemDto(TenantInvoiceItemSto invoiceItemSto) {
        return TenantInvoiceItemDto.builder()
                .itemName(invoiceItemSto.getItemName())
                .from(invoiceItemSto.getFrom())
                .to(invoiceItemSto.getTo())
                .price(invoiceItemSto.getPrice())
                .quantity(invoiceItemSto.getQuantity())
                .build();
    }
}
