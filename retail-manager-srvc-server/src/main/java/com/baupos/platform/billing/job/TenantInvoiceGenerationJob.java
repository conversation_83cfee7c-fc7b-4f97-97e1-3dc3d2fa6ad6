package com.baupos.platform.billing.job;

import com.baupos.platform.billing.service.TenantInvoiceService;
import com.baupos.platform.subscription.service.TenantSubscriptionService;
import com.baupos.platform.subscription.service.sto.TenantSubscriptionSto;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Slice;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TenantInvoiceGenerationJob {

    private final TenantSubscriptionService tenantSubscriptionService;
    private final TenantInvoiceService tenantInvoiceService;

    @Scheduled(cron="0 0 0 1 1/1 *")
    public void generateMonthlyInvoicesForActiveSubscriptions() {
        Slice<TenantSubscriptionSto> subscriptionsPendingInvoiceGeneration;
        do {
            subscriptionsPendingInvoiceGeneration = tenantSubscriptionService.findSubscriptionsPendingMonthlyInvoiceGeneration();
            subscriptionsPendingInvoiceGeneration.forEach(tenantInvoiceService::generateMonthlyInvoiceForSubscription);
        } while (subscriptionsPendingInvoiceGeneration.hasNext());
    }
}
