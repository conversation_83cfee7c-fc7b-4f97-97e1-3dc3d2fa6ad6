package com.baupos.platform.billing.job;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.platform.subscription.domain.TenantSubscriptionRepository;
import com.baupos.platform.subscription.domain.TenantSubscriptionStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

import static com.baupos.platform.billing.domain.TenantInvoiceStatus.PAYMENT_PENDING;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class TenantInvoiceGenerationJobIT extends AbstractRetailManagerServerIT {

    @Autowired
    private TenantInvoiceGenerationJob tenantInvoiceGenerationJob;
    @Autowired
    private TenantSubscriptionRepository tenantSubscriptionRepository;
    @Autowired
    private TransactionTaskRunner transactionTaskRunner;

    @Test
    public void generateInvoiceForActiveSubscriptionsOnly() {
        RetailManagerServerGivens.GivenTenantContext activeSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.ACTIVE)
                .build());
        RetailManagerServerGivens.GivenTenantContext suspendedSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.SUSPENDED)
                .build());
        RetailManagerServerGivens.GivenTenantContext trialSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.TRIAL)
                .build());

        // Given job runs
        tenantInvoiceGenerationJob.generateMonthlyInvoicesForActiveSubscriptions();

        // Verify an invoice was generated for the active subscription only
        transactionTaskRunner.readOnly(() -> {
            var activeSubscription = tenantSubscriptionRepository.findByTenantId(activeSubscriptionTenant.tenant().getId()).get(0);
            assertThat(activeSubscription.getInvoices().size()).isEqualTo(1);
            assertThat(activeSubscription.getInvoices().get(0).getStatus()).isEqualTo(PAYMENT_PENDING);
            assertThat(activeSubscription.getInvoices().get(0).getItems().size()).isEqualTo(1);
            assertThat(activeSubscription.getInvoices().get(0).getInvoiceTotal()).isEqualByComparingTo(new BigDecimal(40000));

            var suspendedSubscription = tenantSubscriptionRepository.findByTenantId(suspendedSubscriptionTenant.tenant().getId()).get(0);
            assertThat(suspendedSubscription.getInvoices().size()).isEqualTo(0);

            var trialSubscription = tenantSubscriptionRepository.findByTenantId(trialSubscriptionTenant.tenant().getId()).get(0);
            assertThat(trialSubscription.getInvoices().size()).isEqualTo(0);
        });

    }
}
