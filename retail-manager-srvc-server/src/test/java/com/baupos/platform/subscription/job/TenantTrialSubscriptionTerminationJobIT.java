package com.baupos.platform.subscription.job;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.platform.subscription.domain.TenantSubscriptionRepository;
import com.baupos.platform.subscription.domain.TenantSubscriptionStatus;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class TenantTrialSubscriptionTerminationJobIT extends AbstractRetailManagerServerIT {

    @Autowired
    private TenantTrialSubscriptionTerminationJob tenantTrialSubscriptionTerminationJob;
    @Autowired
    private TenantSubscriptionRepository tenantSubscriptionRepository;
    @Autowired
    private TransactionTaskRunner transactionTaskRunner;

    @Test
    public void terminateExpiredTrialSubscriptions() {
        RetailManagerServerGivens.GivenTenantContext expiredTrialSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.TRIAL)
                .build());
        RetailManagerServerGivens.GivenTenantContext terminatedTrialSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.TRIAL_ENDED)
                .build());
        RetailManagerServerGivens.GivenTenantContext activeSubscriptionTenant = given().tenant(RetailManagerServerGivens.GivenTenantContextParameters.builder()
                .subscriptionStatus(TenantSubscriptionStatus.ACTIVE)
                .build());

        // Set cock to a future date, so that the above subscriptions are considered older than 15 days
        mockNowTime(LocalDateTime.now().plusDays(16));

        // Given job runs
        tenantTrialSubscriptionTerminationJob.terminateExpiredTrialSubscriptions();

        // Verify expired trial subscription was terminated and the other subscriptions were not changed
        transactionTaskRunner.readOnly(() -> {
            var expiredTrialSubscription = tenantSubscriptionRepository.findByTenantId(expiredTrialSubscriptionTenant.tenant().getId()).get(0);
            assertThat(expiredTrialSubscription.getStatus()).isEqualTo(TenantSubscriptionStatus.TRIAL_ENDED);

            var alreadyTerminatedTrialSubscription = tenantSubscriptionRepository.findByTenantId(terminatedTrialSubscriptionTenant.tenant().getId()).get(0);
            assertThat(alreadyTerminatedTrialSubscription.getStatus()).isEqualTo(TenantSubscriptionStatus.TRIAL_ENDED);

            var activeSubscription = tenantSubscriptionRepository.findByTenantId(activeSubscriptionTenant.tenant().getId()).get(0);
            assertThat(activeSubscription.getStatus()).isEqualTo(TenantSubscriptionStatus.ACTIVE);
        });
    }
}
