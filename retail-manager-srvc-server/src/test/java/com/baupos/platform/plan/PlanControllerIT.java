package com.baupos.platform.plan;

import com.baupos.platform.plan.dto.SubscriptionPlanDto;
import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import org.junit.jupiter.api.Test;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class PlanControllerIT extends AbstractRetailManagerServerIT {

    @Test
    public void testListSubscriptionPlans() {
        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/subscription-plans")
                .build();

        List<SubscriptionPlanDto> plans = restTemplate().getForObject(uriBuilder.toUriString(), List.class);
        assertThat(plans).hasSize(3);
    }

}
