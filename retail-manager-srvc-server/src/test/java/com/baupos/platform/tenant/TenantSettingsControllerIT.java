package com.baupos.platform.tenant;

import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.RetailManagerServerGivens.GivenUserContextParameters;
import com.baupos.platform.tenant.dto.TenantSettingsDto;
import com.baupos.platform.tenant.dto.TimezoneOptionsDto;
import com.baupos.platform.tenant.dto.UpdateTenantSettingsRequestDto;
import com.baupos.retailmanager.user.domain.Privilege;
import com.baupos.retailmanager.user.domain.Role;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@Slf4j
public class TenantSettingsControllerIT extends AbstractRetailManagerServerIT {

    @Test
    public void testGetTenantSettingsWhenEmpty() {
        // Given tenant with user (any user can read tenant settings)
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .build());

        // When authenticated as user
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/")
                .build();

        // Then should return empty settings
        ResponseEntity<TenantSettingsDto> response = restTemplate().getForEntity(uriBuilder.toUriString(), TenantSettingsDto.class);
        
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        TenantSettingsDto settings = response.getBody();
        assertThat(settings).isNotNull();
        assertThat(settings.getCompanyName()).isNull();
        assertThat(settings.getLocale()).isNull();
        assertThat(settings.getTimezone()).isNull();
        assertThat(settings.getEmailAddress()).isNull();
        assertThat(settings.getPhoneNumber()).isNull();
        assertThat(settings.getCountry()).isNull();
        assertThat(settings.getCity()).isNull();
        assertThat(settings.getAddress()).isNull();
    }

    @Test
    public void testUpdateTenantSettingsWithTenantManagementPrivilege() {
        // Given tenant with user having TENANT_MANAGEMENT privilege
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.TENANT_MANAGEMENT))
                        .build());

        // When authenticated as user with TENANT_MANAGEMENT privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/")
                .build();

        UpdateTenantSettingsRequestDto updateRequest = UpdateTenantSettingsRequestDto.builder()
                .companyName("Test Company")
                .locale("en_US")
                .timezone("America/New_York")
                .emailAddress("<EMAIL>")
                .phoneNumber("******-123-4567")
                .country("United States")
                .city("New York")
                .address("123 Main Street")
                .build();

        // Then should successfully update settings
        TenantSettingsDto updatedSettings = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                TenantSettingsDto.class)
                .getBody();
        
        assertThat(updatedSettings).isNotNull();
        assertThat(updatedSettings.getCompanyName()).isEqualTo("Test Company");
        assertThat(updatedSettings.getLocale()).isEqualTo("en_US");
        assertThat(updatedSettings.getTimezone()).isEqualTo("America/New_York");
        assertThat(updatedSettings.getEmailAddress()).isEqualTo("<EMAIL>");
        assertThat(updatedSettings.getPhoneNumber()).isEqualTo("******-123-4567");
        assertThat(updatedSettings.getCountry()).isEqualTo("United States");
        assertThat(updatedSettings.getCity()).isEqualTo("New York");
        assertThat(updatedSettings.getAddress()).isEqualTo("123 Main Street");
    }

    @Test
    public void testGetTenantSettingsAfterUpdate() {
        // Given tenant with user having TENANT_MANAGEMENT privilege and existing settings
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.TENANT_MANAGEMENT))
                        .build());

        // When authenticated as user with TENANT_MANAGEMENT privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/")
                .build();

        // First create settings
        UpdateTenantSettingsRequestDto updateRequest = UpdateTenantSettingsRequestDto.builder()
                .companyName("Test Company")
                .locale("en_US")
                .timezone("America/New_York")
                .emailAddress("<EMAIL>")
                .phoneNumber("******-123-4567")
                .country("United States")
                .city("New York")
                .address("123 Main Street")
                .build();

        restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                TenantSettingsDto.class);

        // Then should be able to retrieve the settings
        TenantSettingsDto retrievedSettings = restTemplate().getForObject(uriBuilder.toUriString(), TenantSettingsDto.class);
        
        assertThat(retrievedSettings).isNotNull();
        assertThat(retrievedSettings.getCompanyName()).isEqualTo("Test Company");
        assertThat(retrievedSettings.getLocale()).isEqualTo("en_US");
        assertThat(retrievedSettings.getTimezone()).isEqualTo("America/New_York");
        assertThat(retrievedSettings.getEmailAddress()).isEqualTo("<EMAIL>");
        assertThat(retrievedSettings.getPhoneNumber()).isEqualTo("******-123-4567");
        assertThat(retrievedSettings.getCountry()).isEqualTo("United States");
        assertThat(retrievedSettings.getCity()).isEqualTo("New York");
        assertThat(retrievedSettings.getAddress()).isEqualTo("123 Main Street");
    }

    @Test
    public void testUpdateTenantSettingsWithoutTenantManagementPrivilege() {
        // Given tenant with user without TENANT_MANAGEMENT privilege
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of()) // No TENANT_MANAGEMENT privilege
                        .build());

        // When authenticated as user without TENANT_MANAGEMENT privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/")
                .build();

        UpdateTenantSettingsRequestDto updateRequest = UpdateTenantSettingsRequestDto.builder()
                .companyName("Test Company")
                .build();

        // Then should get access denied
        assertThatThrownBy(() -> restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                TenantSettingsDto.class))
                .isInstanceOf(HttpClientErrorException.class)
                .hasMessageContaining("401"); // 401 Unauthorized because user doesn't have TENANT_MANAGEMENT privilege
    }

    @Test
    public void testUpdateTenantSettingsMultipleTimes() {
        // Given tenant with user having TENANT_MANAGEMENT privilege
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.TENANT_MANAGEMENT))
                        .build());

        // When authenticated as user with TENANT_MANAGEMENT privilege
        authenticateUser(userContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/")
                .build();

        // First update
        UpdateTenantSettingsRequestDto firstUpdate = UpdateTenantSettingsRequestDto.builder()
                .companyName("First Company")
                .locale("en_US")
                .build();

        TenantSettingsDto firstResult = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(firstUpdate),
                TenantSettingsDto.class)
                .getBody();
        assertThat(firstResult.getCompanyName()).isEqualTo("First Company");
        assertThat(firstResult.getLocale()).isEqualTo("en_US");

        // Second update (should update existing record)
        UpdateTenantSettingsRequestDto secondUpdate = UpdateTenantSettingsRequestDto.builder()
                .companyName("Updated Company")
                .locale("es_ES")
                .timezone("Europe/Madrid")
                .build();

        TenantSettingsDto secondResult = restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(secondUpdate),
                TenantSettingsDto.class)
                .getBody();
        assertThat(secondResult.getCompanyName()).isEqualTo("Updated Company");
        assertThat(secondResult.getLocale()).isEqualTo("es_ES");
        assertThat(secondResult.getTimezone()).isEqualTo("Europe/Madrid");
    }

    @Test
    public void testTenantIsolation() {
        // Given two different tenants with users having TENANT_MANAGEMENT privilege
        RetailManagerServerGivens.GivenTenantContext tenant1Context = given().tenant();
        RetailManagerServerGivens.GivenUserContext user1Context = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenant1Context.tenant().getId())
                        .privileges(List.of(Privilege.TENANT_MANAGEMENT))
                        .build());

        RetailManagerServerGivens.GivenTenantContext tenant2Context = given().tenant();
        RetailManagerServerGivens.GivenUserContext user2Context = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenant2Context.tenant().getId())
                        .privileges(List.of(Privilege.TENANT_MANAGEMENT))
                        .build());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/")
                .build();

        // When user1 creates settings for tenant1
        authenticateUser(user1Context.user().getUsername());
        UpdateTenantSettingsRequestDto tenant1Update = UpdateTenantSettingsRequestDto.builder()
                .companyName("Tenant 1 Company")
                .build();
        restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(tenant1Update),
                TenantSettingsDto.class);

        // Clear security context before switching users
        SecurityContextHolder.getContext().setAuthentication(null);

        // When user2 creates settings for tenant2
        authenticateUser(user2Context.user().getUsername());
        UpdateTenantSettingsRequestDto tenant2Update = UpdateTenantSettingsRequestDto.builder()
                .companyName("Tenant 2 Company")
                .build();
        restTemplate().exchange(
                uriBuilder.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(tenant2Update),
                TenantSettingsDto.class);

        // Then user2 should only see tenant2's settings
        TenantSettingsDto tenant2Settings = restTemplate().getForObject(uriBuilder.toUriString(), TenantSettingsDto.class);
        assertThat(tenant2Settings.getCompanyName()).isEqualTo("Tenant 2 Company");

        // Clear security context before switching users
        SecurityContextHolder.getContext().setAuthentication(null);

        // And user1 should only see tenant1's settings
        authenticateUser(user1Context.user().getUsername());
        TenantSettingsDto tenant1Settings = restTemplate().getForObject(uriBuilder.toUriString(), TenantSettingsDto.class);
        assertThat(tenant1Settings.getCompanyName()).isEqualTo("Tenant 1 Company");
    }

    @Test
    public void testGetTimezoneOptions() {
        // Given a user with read access to tenant settings
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of(Privilege.TENANT_MANAGEMENT))
                        .build());

        authenticateUser(userContext.user().getUsername());

        // First set a locale for the tenant
        UriComponents settingsUri = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/")
                .build();

        UpdateTenantSettingsRequestDto updateRequest = UpdateTenantSettingsRequestDto.builder()
                .locale("es_ES")
                .build();

        restTemplate().exchange(
                settingsUri.toUriString(),
                HttpMethod.PUT,
                new HttpEntity<>(updateRequest),
                TenantSettingsDto.class);

        // When requesting timezone options
        UriComponents timezoneUri = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/timezone-options")
                .build();

        TimezoneOptionsDto timezoneOptions = restTemplate().getForObject(timezoneUri.toUriString(), TimezoneOptionsDto.class);

        // Then should return timezone options
        assertThat(timezoneOptions).isNotNull();
        assertThat(timezoneOptions.getTimezones()).isNotNull();
        assertThat(timezoneOptions.getTimezones()).isNotEmpty();

        // Should contain common timezones
        assertThat(timezoneOptions.getTimezones()).containsKey("America/New_York");
        assertThat(timezoneOptions.getTimezones()).containsKey("Europe/Madrid");
        assertThat(timezoneOptions.getTimezones()).containsKey("UTC");

        // Values should be display names (not just the timezone IDs)
        assertThat(timezoneOptions.getTimezones().get("America/New_York")).isNotEqualTo("America/New_York");
        assertThat(timezoneOptions.getTimezones().get("Europe/Madrid")).isNotEqualTo("Europe/Madrid");
    }

    @Test
    public void testGetTimezoneOptionsWithoutTenantManagementPrivilege() {
        // Given a user without TENANT_MANAGEMENT privilege (but still logged in)
        RetailManagerServerGivens.GivenTenantContext tenantContext = given().tenant();
        RetailManagerServerGivens.GivenUserContext userContext = given()
                .user(GivenUserContextParameters.builder()
                        .role(Role.EMPLOYEE)
                        .tenantId(tenantContext.tenant().getId())
                        .privileges(List.of()) // No privileges
                        .build());

        authenticateUser(userContext.user().getUsername());

        // When requesting timezone options
        UriComponents timezoneUri = UriComponentsBuilder.newInstance()
                .path("/v1/tenant/settings/timezone-options")
                .build();

        // Then should still be able to access (since canReadTenantSettings allows any logged-in user)
        TimezoneOptionsDto timezoneOptions = restTemplate().getForObject(timezoneUri.toUriString(), TimezoneOptionsDto.class);

        assertThat(timezoneOptions).isNotNull();
        assertThat(timezoneOptions.getTimezones()).isNotNull();
        assertThat(timezoneOptions.getTimezones()).isNotEmpty();
    }
}
