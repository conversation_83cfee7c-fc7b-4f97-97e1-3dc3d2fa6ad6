package com.baupos.platform.tenant;

import com.baupos.platform.plan.domain.SubscriptionPlanCode;
import com.baupos.platform.subscription.domain.TenantSubscription;
import com.baupos.platform.subscription.dto.CreateSubscriptionRequestDto;
import com.baupos.platform.subscription.dto.CreateSubscriptionResponseDto;
import com.baupos.platform.tenant.domain.Tenant;
import com.baupos.platform.tenant.domain.TenantRepository;
import com.baupos.retailmanager.AbstractRetailManagerServerIT;
import com.baupos.retailmanager.RetailManagerServerGivens;
import com.baupos.retailmanager.common.error.ErrorResponse;
import com.baupos.retailmanager.common.utils.TransactionTaskRunner;
import com.baupos.retailmanager.user.service.UserService;
import com.baupos.retailmanager.user.service.sto.UserSto;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import static com.baupos.platform.subscription.domain.TenantSubscriptionStatus.TRIAL;
import static com.baupos.retailmanager.common.error.CmErrorCode.OPERATION_NOT_SUPPORTED_FOR_EXISTING_USERS;
import static com.baupos.retailmanager.user.domain.Role.OWNER;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

public class OnboardingControllerIT extends AbstractRetailManagerServerIT {

    @Autowired
    private UserService userService;
    @Autowired
    private TenantRepository tenantRepository;
    @Autowired
    private TransactionTaskRunner transactionTaskRunner;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Ignore
    public void createSubscriptionWhenAlreadyAuthenticatedFails() {
        RetailManagerServerGivens.GivenUserContext existingUserContext = given().user();
        authenticateUser(existingUserContext.user().getUsername());

        UriComponents uriBuilder = UriComponentsBuilder.newInstance().path("/v1/onboarding/subscription").build();

        var request = CreateSubscriptionRequestDto.builder()
                .username("<EMAIL>")
                .password("Welcome@123")
                .build();

        assertThatExceptionOfType(HttpClientErrorException.class)
                .isThrownBy(() -> restTemplate().postForObject(uriBuilder.toUriString(), request, CreateSubscriptionResponseDto.class))
                .satisfies(e -> {
                    ErrorResponse response = e.getResponseBodyAs(ErrorResponse.class);
                    assertThat(response.getCodeName()).isEqualTo(OPERATION_NOT_SUPPORTED_FOR_EXISTING_USERS.name());
                });
    }

    @Test
    public void createSubscriptionHappyPath() {
        UriComponents uriBuilder = UriComponentsBuilder.newInstance().path("/v1/onboarding/subscription").build();
        String username = "<EMAIL>";
        String password = "Welcome@123!";

        var request = CreateSubscriptionRequestDto.builder()
                .username(username)
                .password(password)
                .build();

        CreateSubscriptionResponseDto response = restTemplate().postForObject(uriBuilder.toUriString(), request, CreateSubscriptionResponseDto.class);

        // Assert new user was created successfully
        UserSto newUser = userService.getById(response.getUser().getId());
        assertThat(newUser.getUsername()).isEqualTo(username);
        assertThat(passwordEncoder.matches(password, newUser.getPassword())).isTrue();
        assertThat(newUser.getRole()).isEqualTo(OWNER);

        // Assert tenant and subscription was created successfully
        transactionTaskRunner.readOnly(() -> {
            Tenant newTenant = tenantRepository.getReferenceById(newUser.getTenantId());
            assertThat(newTenant.getSubscriptions()).hasSize(1);
            TenantSubscription tenantSubscription = newTenant.getSubscriptions().get(0);
            assertThat(tenantSubscription.getPlan().getCode()).isEqualTo(SubscriptionPlanCode.STARTER);
            assertThat(tenantSubscription.getStatus()).isEqualTo(TRIAL);
        });
    }
}
